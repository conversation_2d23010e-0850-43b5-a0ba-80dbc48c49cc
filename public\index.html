<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Lua Script Obfuscator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> Advanced Lua Obfuscator</h1>
                <p>Transform your Lua scripts into heavily obfuscated, reverse-engineering resistant code</p>
            </div>
        </header>

        <main class="main-content">
            <div class="obfuscator-panel">
                <div class="input-section">
                    <div class="section-header">
                        <h2><i class="fas fa-code"></i> Original Lua Code</h2>
                        <div class="section-actions">
                            <button id="clearInput" class="btn btn-secondary">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                            <button id="loadExample" class="btn btn-secondary">
                                <i class="fas fa-file-code"></i> Load Example
                            </button>
                        </div>
                    </div>
                    <textarea 
                        id="inputCode" 
                        placeholder="-- Enter your Lua code here
-- Example:
print('Hello, World!')
local x = 42
for i = 1, 10 do
    print('Number: ' .. i)
end"
                        spellcheck="false"
                    ></textarea>
                    <div class="input-info">
                        <span id="inputStats">Characters: 0 | Lines: 0</span>
                    </div>
                </div>

                <div class="control-section">
                    <button id="obfuscateBtn" class="btn btn-primary">
                        <i class="fas fa-lock"></i> Obfuscate Code
                    </button>
                    <div class="obfuscation-options">
                        <div class="option-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="removeComments" checked>
                                <span class="checkmark"></span>
                                Remove Comments & Whitespace
                            </label>
                        </div>
                        <div class="option-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="multiLayer" checked>
                                <span class="checkmark"></span>
                                Multi-Layer Encryption
                            </label>
                        </div>
                        <div class="option-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="vmDecoder" checked>
                                <span class="checkmark"></span>
                                Virtual Machine Decoder
                            </label>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div class="section-header">
                        <h2><i class="fas fa-shield-alt"></i> Obfuscated Code</h2>
                        <div class="section-actions">
                            <button id="copyOutput" class="btn btn-success" disabled>
                                <i class="fas fa-copy"></i> Copy to Clipboard
                            </button>
                            <button id="downloadOutput" class="btn btn-secondary" disabled>
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                    <textarea 
                        id="outputCode" 
                        placeholder="Obfuscated code will appear here..."
                        readonly
                        spellcheck="false"
                    ></textarea>
                    <div class="output-info">
                        <span id="outputStats">Ready to obfuscate</span>
                        <div id="processingTime" class="processing-time"></div>
                    </div>
                </div>
            </div>

            <div class="info-panel">
                <div class="info-section">
                    <h3><i class="fas fa-info-circle"></i> Obfuscation Features</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Base64 encoding layer</li>
                        <li><i class="fas fa-check"></i> XOR encryption with random keys</li>
                        <li><i class="fas fa-check"></i> Virtual machine-like decoder</li>
                        <li><i class="fas fa-check"></i> Variable name randomization</li>
                        <li><i class="fas fa-check"></i> Multi-layer decryption process</li>
                        <li><i class="fas fa-check"></i> Anti-reverse engineering techniques</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3><i class="fas fa-exclamation-triangle"></i> Important Notes</h3>
                    <ul class="warning-list">
                        <li>Obfuscated code maintains full functionality</li>
                        <li>Reverse engineering is extremely difficult</li>
                        <li>Keep original code as backup</li>
                        <li>Test obfuscated code before deployment</li>
                    </ul>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Advanced Lua Obfuscator. Built for maximum security and reverse-engineering resistance.</p>
        </footer>
    </div>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-cog fa-spin"></i>
            <p>Obfuscating your code...</p>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script src="script.js"></script>
</body>
</html>
