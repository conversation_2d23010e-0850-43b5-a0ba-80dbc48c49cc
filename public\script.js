class LuaObfuscatorApp {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.updateInputStats();
    }

    initializeElements() {
        // Input elements
        this.inputCode = document.getElementById('inputCode');
        this.outputCode = document.getElementById('outputCode');
        this.obfuscateBtn = document.getElementById('obfuscateBtn');
        
        // Button elements
        this.clearInput = document.getElementById('clearInput');
        this.loadExample = document.getElementById('loadExample');
        this.copyOutput = document.getElementById('copyOutput');
        this.downloadOutput = document.getElementById('downloadOutput');
        
        // Info elements
        this.inputStats = document.getElementById('inputStats');
        this.outputStats = document.getElementById('outputStats');
        this.processingTime = document.getElementById('processingTime');
        
        // Overlay elements
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.notification = document.getElementById('notification');
        
        // Option elements
        this.removeComments = document.getElementById('removeComments');
        this.multiLayer = document.getElementById('multiLayer');
        this.vmDecoder = document.getElementById('vmDecoder');
    }

    attachEventListeners() {
        // Main functionality
        this.obfuscateBtn.addEventListener('click', () => this.obfuscateCode());
        this.inputCode.addEventListener('input', () => this.updateInputStats());
        
        // Button actions
        this.clearInput.addEventListener('click', () => this.clearInputCode());
        this.loadExample.addEventListener('click', () => this.loadExampleCode());
        this.copyOutput.addEventListener('click', () => this.copyToClipboard());
        this.downloadOutput.addEventListener('click', () => this.downloadCode());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.obfuscateCode();
            }
        });
    }

    updateInputStats() {
        const code = this.inputCode.value;
        const characters = code.length;
        const lines = code.split('\n').length;
        this.inputStats.textContent = `Characters: ${characters} | Lines: ${lines}`;
    }

    clearInputCode() {
        this.inputCode.value = '';
        this.outputCode.value = '';
        this.updateInputStats();
        this.updateOutputStats('Ready to obfuscate');
        this.toggleOutputButtons(false);
    }

    loadExampleCode() {
        const exampleCode = `-- Lua Example Script
local function fibonacci(n)
    if n <= 1 then
        return n
    else
        return fibonacci(n-1) + fibonacci(n-2)
    end
end

print("Fibonacci sequence:")
for i = 0, 10 do
    print("F(" .. i .. ") = " .. fibonacci(i))
end

-- Table operations
local myTable = {
    name = "Lua Obfuscator",
    version = "1.0",
    features = {"encryption", "obfuscation", "security"}
}

for key, value in pairs(myTable) do
    if type(value) == "table" then
        print(key .. ": " .. table.concat(value, ", "))
    else
        print(key .. ": " .. tostring(value))
    end
end`;

        this.inputCode.value = exampleCode;
        this.updateInputStats();
        this.showNotification('Example code loaded successfully!', 'success');
    }

    async obfuscateCode() {
        const code = this.inputCode.value.trim();
        
        if (!code) {
            this.showNotification('Please enter some Lua code to obfuscate.', 'error');
            return;
        }

        this.showLoading(true);
        this.obfuscateBtn.disabled = true;
        
        const startTime = Date.now();

        try {
            const response = await fetch('/api/obfuscate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code })
            });

            const result = await response.json();
            const processingTime = Date.now() - startTime;

            if (result.success) {
                this.outputCode.value = result.obfuscated;
                this.updateOutputStats(
                    `Obfuscated successfully | Original: ${result.originalSize} chars | ` +
                    `Obfuscated: ${result.obfuscatedSize} chars | Ratio: ${result.compressionRatio}x`
                );
                this.processingTime.textContent = `Processing time: ${processingTime}ms`;
                this.toggleOutputButtons(true);
                this.showNotification('Code obfuscated successfully!', 'success');
            } else {
                this.showNotification(`Obfuscation failed: ${result.error}`, 'error');
                this.updateOutputStats('Obfuscation failed');
            }
        } catch (error) {
            this.showNotification(`Network error: ${error.message}`, 'error');
            this.updateOutputStats('Network error occurred');
        } finally {
            this.showLoading(false);
            this.obfuscateBtn.disabled = false;
        }
    }

    async copyToClipboard() {
        try {
            await navigator.clipboard.writeText(this.outputCode.value);
            this.showNotification('Code copied to clipboard!', 'success');
        } catch (error) {
            // Fallback for older browsers
            this.outputCode.select();
            document.execCommand('copy');
            this.showNotification('Code copied to clipboard!', 'success');
        }
    }

    downloadCode() {
        const code = this.outputCode.value;
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'obfuscated_script.lua';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Code downloaded successfully!', 'success');
    }

    updateOutputStats(message) {
        this.outputStats.textContent = message;
    }

    toggleOutputButtons(enabled) {
        this.copyOutput.disabled = !enabled;
        this.downloadOutput.disabled = !enabled;
    }

    showLoading(show) {
        this.loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    showNotification(message, type = 'info') {
        this.notification.textContent = message;
        this.notification.className = `notification ${type} show`;
        
        setTimeout(() => {
            this.notification.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LuaObfuscatorApp();
});
