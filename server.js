const express = require('express');
const cors = require('cors');
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Utility functions for obfuscation
class LuaObfuscator {
    constructor() {
        this.charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    }

    // Generate random string for variable names
    generateRandomName(length = 8) {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += this.charset.charAt(Math.floor(Math.random() * this.charset.length));
        }
        return result;
    }

    // Base64 encoding
    base64Encode(str) {
        return Buffer.from(str, 'utf8').toString('base64');
    }

    // XOR encryption with key
    xorEncrypt(data, key) {
        let result = '';
        for (let i = 0; i < data.length; i++) {
            result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
        }
        return result;
    }

    // Convert string to byte array representation
    stringToByteArray(str) {
        const bytes = [];
        for (let i = 0; i < str.length; i++) {
            bytes.push(str.charCodeAt(i));
        }
        return bytes;
    }

    // Generate virtual machine-like decoder with multiple layers
    generateDecoder(encryptedData, xorKey) {
        const vars = {
            decoder: this.generateRandomName(),
            data: this.generateRandomName(),
            key: this.generateRandomName(),
            result: this.generateRandomName(),
            i: this.generateRandomName(),
            temp: this.generateRandomName(),
            exec: this.generateRandomName(),
            vm: this.generateRandomName(),
            stage1: this.generateRandomName(),
            stage2: this.generateRandomName(),
            stage3: this.generateRandomName(),
            loader: this.generateRandomName(),
            runner: this.generateRandomName()
        };

        const encryptedBytes = this.stringToByteArray(encryptedData);
        const keyBytes = this.stringToByteArray(xorKey);

        // Create multiple dummy variables to confuse analysis
        const dummyVars = Array.from({length: 10}, () => this.generateRandomName());
        const dummyAssignments = dummyVars.map(v => `local ${v}=${Math.floor(Math.random() * 1000)}`).join(' ');

        // Virtual machine-like decoder with multiple stages
        const decoder = `${dummyAssignments} local ${vars.decoder}={${encryptedBytes.join(',')}} local ${vars.key}={${keyBytes.join(',')}} local ${vars.result}="" local ${vars.vm}=function(${vars.data},${vars.exec}) local ${vars.temp}="" for ${vars.i}=1,#${vars.data} do ${vars.temp}=${vars.temp}..string.char(${vars.data}[${vars.i}]~${vars.exec}[((${vars.i}-1)%#${vars.exec})+1]) end return ${vars.temp} end local ${vars.stage1}=${vars.vm}(${vars.decoder},${vars.key}) local ${vars.stage2}=${vars.stage1}:gsub("(.)(.)(.)(.)",""):gsub("(.)(.)(.)",function(a,b,c)return string.char(((a:byte()-65)*4096)+((b:byte()-65)*64)+(c:byte()-65))end) local ${vars.loader}=load(${vars.stage2}) local ${vars.runner}=${vars.loader} ${vars.runner}()`;

        return decoder;
    }

    // Generate advanced decoder with multiple decryption layers
    generateAdvancedDecoder(encryptedData, keys) {
        const vars = {
            data: this.generateRandomName(),
            key1: this.generateRandomName(),
            key2: this.generateRandomName(),
            vm1: this.generateRandomName(),
            vm2: this.generateRandomName(),
            vm3: this.generateRandomName(),
            stage1: this.generateRandomName(),
            stage2: this.generateRandomName(),
            stage3: this.generateRandomName(),
            stage4: this.generateRandomName(),
            final: this.generateRandomName(),
            exec: this.generateRandomName()
        };

        const dataBytes = this.stringToByteArray(encryptedData);
        const key1Bytes = this.stringToByteArray(keys[0]);
        const key2Bytes = this.stringToByteArray(keys[1]);

        // Create complex decoder with multiple virtual machines
        const decoder = `local ${vars.data}={${dataBytes.join(',')}} local ${vars.key1}={${key1Bytes.join(',')}} local ${vars.key2}={${key2Bytes.join(',')}} local ${vars.vm1}=function(d,k) local r="" for i=1,#d do r=r..string.char(d[i]~k[((i-1)%#k)+1]) end return r end local ${vars.vm2}=function(d,k) local r="" for i=1,#d do r=r..string.char(d[i]~k[((i-1)%#k)+1]) end return r end local ${vars.vm3}=function(s) return s:gsub("(.)(.)(.)",function(a,b,c) return string.char(((a:byte()-65)*4096)+((b:byte()-65)*64)+(c:byte()-65)) end) end local ${vars.stage1}=${vars.vm1}(${vars.data},${vars.key2}) local ${vars.stage2}=${vars.vm2}(${vars.stage1}:byte(1,#${vars.stage1}),${vars.key1}) local ${vars.stage3}=${vars.vm3}(${vars.stage2}) local ${vars.stage4}=${vars.stage3}:gsub("(.)(.)(.)(.)","") local ${vars.final}=load(${vars.stage4}) ${vars.final}()`;

        return decoder;
    }

    // Add string scrambling layer
    scrambleString(str) {
        const chunks = [];
        for (let i = 0; i < str.length; i += 3) {
            chunks.push(str.substr(i, 3));
        }
        return chunks.map(chunk => {
            const padded = chunk.padEnd(3, 'A');
            return String.fromCharCode(
                ((padded.charCodeAt(0) - 65) * 4096) +
                ((padded.charCodeAt(1) - 65) * 64) +
                (padded.charCodeAt(2) - 65)
            );
        }).join('');
    }

    // Add multiple encryption layers
    multiLayerEncrypt(data) {
        // Layer 1: Base64
        let result = this.base64Encode(data);

        // Layer 2: String scrambling
        result = this.scrambleString(result);

        // Layer 3: XOR with multiple keys
        const key1 = this.generateRandomName(12);
        const key2 = this.generateRandomName(8);
        result = this.xorEncrypt(result, key1);
        result = this.xorEncrypt(result, key2);

        return { data: result, keys: [key1, key2] };
    }

    // Main obfuscation function with enhanced security
    obfuscate(luaCode) {
        try {
            // Multi-layer encryption
            const encrypted = this.multiLayerEncrypt(luaCode);

            // Generate the virtual machine decoder
            const obfuscatedCode = this.generateAdvancedDecoder(encrypted.data, encrypted.keys);

            return {
                success: true,
                obfuscated: obfuscatedCode,
                originalSize: luaCode.length,
                obfuscatedSize: obfuscatedCode.length,
                compressionRatio: (obfuscatedCode.length / luaCode.length).toFixed(2)
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// API Routes
app.post('/api/obfuscate', (req, res) => {
    const { code } = req.body;
    
    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }
    
    const obfuscator = new LuaObfuscator();
    const result = obfuscator.obfuscate(code);
    
    res.json(result);
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`Lua Obfuscator server running on http://localhost:${PORT}`);
});
